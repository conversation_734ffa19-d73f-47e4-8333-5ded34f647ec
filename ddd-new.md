.
├── api
│   ├── apply
│   │   ├── api.ts
│   │   ├── mock.ts
│   │   └── translator.ts
│   ├── contract
│   │   ├── api.ts
│   │   └── translator.ts
│   └── user
│       ├── index.ts
│       └── translator.ts
├── app.scss
├── app.tsx
├── assets
│   └── img
│       └── selfie_3x.png
├── components
│   └── demo
│       └── index.tsx
├── constants
│   ├── index-new.ts
│   ├── index.ts
│   ├── mock-config.ts
│   ├── setup-config.ts
│   ├── setupConfig.ts
│   └── theme.ts
├── domain
│   ├── models
│   │   ├── apply
│   │   │   ├── apply.ts            # 聚合根
│   │   │   ├── identity-card.ts    # 实体
│   │   │   ├── merchant.vo.ts      # 值对象
│   │   │   └── personal-info.vo.ts
│   │   ├── contract
│   │   │   └── contract.ts
│   │   └── user
│   │       └── user.ts
│   ├── repositories                # 仓储，新增
│   │   ├── apply.repository.ts
│   │   ├── contract.repository.ts
│   │   └── user.repository.ts
│   └── services
│       ├── apply-service.ts
│       ├── contract-service.ts
│       ├── form-validation-service.ts
│       └── user-service.ts
├── index.html
├── pages                       # 表现层
│   ├── external-libs-demo
│   ├── homepage
│   │   ├── components
│   │   │   └── personal-setting
│   │   │       ├── index.scss
│   │   │       └── index.tsx
│   │   ├── index.tsx
│   │   └── store.ts                # UI store
│   ├── identity
│   │   ├── README.md
│   │   ├── components
│   │   │   ├── contract-checker
│   │   │   │   ├── index.scss
│   │   │   │   └── index.tsx
│   │   │   ├── identity-choice
│   │   │   │   ├── index.scss
│   │   │   │   └── index.tsx
│   │   │   ├── popup-picker
│   │   │   │   ├── index.scss
│   │   │   │   └── index.tsx
│   │   │   └── school-info
│   │   │       ├── index.scss
│   │   │       └── index.tsx
│   │   ├── index.scss
│   │   ├── index.tsx
│   │   └── store.ts
│   └── index
│       ├── index.tsx
│       └── store.ts
├── styles
│   ├── mixins.scss
│   ├── reset.scss
│   └── themes
│       ├── default.scss
│       ├── purple.scss
│       └── red.scss
├── types
│   ├── api.d.ts
│   ├── apply.d.ts
│   ├── contract.d.ts
│   └── user.d.ts
└── utils
    ├── contract-params.ts
    ├── data-pool.ts
    ├── h5-login.ts
    └── index.ts